import os
import sys
import requests
import shutil
from pathlib import Path
from getpass import getpass
import importlib
import subprocess
from bs4 import BeautifulSoup
import json
import re
from google.cloud import vision_v1
from google.oauth2.service_account import Credentials
from PIL import Image
from urllib.parse import urljoin

CURRENT_VERSION = "1.0.0"
VERSION_URL = "https://aniverse.fr/protection/version.txt"

AVAILABLE_SOURCES = [
    {'title': 'https://manhuaus.org', 'url': 'https://manhuaus.org'},
    {'title': 'https://manhuafast.net/', 'url': 'https://manhuafast.net/'},
    {'title': 'https://aquareader.net/', 'url': 'https://aquareader.net/'},
    {'title': 'https://manhuaus.com/', 'url': 'https://manhuaus.com/'},
    {'title': 'https://kunmanga.com/', 'url': 'https://kunmanga.com/'},
    {'title': 'https://manhwaclan.com/', 'url': 'https://manhwaclan.com/'},
    {'title': 'https://ravenscans.com/', 'url': 'https://ravenscans.com/'},
]

class VersionMismatchError(Exception):
    """Custom exception for version mismatch."""
    def __init__(self, remote_version, current_version):
        self.remote_version = remote_version
        self.current_version = current_version
        super().__init__(f"Version mismatch! Your version: {current_version}, Latest version: {remote_version}. Please update the script.")

def check_version():
    """Checks if the current version matches the remote version."""
    try:
        response = requests.get(VERSION_URL)
        response.raise_for_status()  # Raise an exception for HTTP errors
        remote_version = response.text.strip()
        if remote_version != CURRENT_VERSION:
            raise VersionMismatchError(remote_version, CURRENT_VERSION) # Raise custom exception
        print(f"Version {CURRENT_VERSION} is up to date.") # This print is fine for CLI
    except requests.exceptions.RequestException as e:
        # For CLI, it's okay to print this error and exit. GUI will handle it differently.
        print(f"Error checking version: {e}")
        sys.exit(1)
    # Do not catch Exception here broadly, let it propagate or be caught by caller

# --- User interaction helpers ---
def choose_from_list(options, prompt):
    for idx, opt in enumerate(options):
        print(f"[{idx+1}] {opt['title']}")
    while True:
        try:
            sel = int(input(prompt))
            if 1 <= sel <= len(options):
                return options[sel-1]
        except Exception:
            pass
        print("Invalid selection. Try again.")

# --- Spoofed headers for all requests ---
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Referer': None,  # Will be set dynamically based on user selection
}

BASE_URL = None  # Will be set dynamically based on user selection

# Initialize Google Vision client
response = requests.get('https://aniverse.fr/protection/credentials.json')
credentials = response.json()
creds = Credentials.from_service_account_info(credentials)
client = vision_v1.ImageAnnotatorClient(credentials=creds)

def convert_images_to_jpg_folder(folder_path):
    """Convert .png and .webp in folder to JPEG for Vision OCR."""
    for fname in os.listdir(folder_path):
        if fname.lower().endswith(('.png', '.webp')):
            fpath = os.path.join(folder_path, fname)
            img = Image.open(fpath).convert('RGB')
            jpg_path = os.path.splitext(fpath)[0] + '.jpg'
            img.save(jpg_path, 'JPEG')
            os.remove(fpath)

def extract_text(image_path):
    """Extract text from image via Google Vision."""
    with open(image_path, 'rb') as img_file:
        content = img_file.read()
    image = vision_v1.types.Image(content=content)
    response = client.text_detection(image=image)
    texts = response.text_annotations
    return texts[0].description if texts else ''

# --- Manga search, chapter, and image logic ---
def search_mangas(search_term):
    url = f'{BASE_URL}/wp-admin/admin-ajax.php'
    data = {'action': 'wp-manga-search-manga', 'title': search_term}
    resp = requests.post(url, data=data, headers=HEADERS)
    try:
        results = resp.json()['data']
        return [{'title': m['title'], 'url': m['url']} for m in results]
    except Exception:
        return []

def search_mangas_ravenscans(search_term):
    url = f'https://ravenscans.com/?s={search_term.replace(" ", "+")}'
    resp = requests.get(url, headers=HEADERS)
    soup = BeautifulSoup(resp.text, 'html.parser')
    results = []
    for a in soup.select('div.listupd div.bs a'):
        results.append({'title': a.get('title', ''), 'url': a.get('href', '')})
    return results

def fetch_chapters(manga_url):
    ajax_url = f"{manga_url.rstrip('/')}/ajax/chapters/"
    resp = requests.post(ajax_url, headers=HEADERS)
    soup = BeautifulSoup(resp.text, 'html.parser')
    chapters = []
    for li in soup.find_all('li', class_='wp-manga-chapter'):
        a = li.find('a')
        if a:
            # Insert at the beginning to reverse the order
            chapters.insert(0, {'title': a.text.strip(), 'url': a['href']})
    return chapters

def fetch_chapters_ravenscans(manga_url):
    """Fetches the list of chapters for a manga from Ravenscans.com."""
    try:
        if not manga_url.startswith(('http://', 'https://')):
            manga_url = urljoin(BASE_URL, manga_url)

        print(f"Fetching chapters from {manga_url}")
        response = requests.get(manga_url, headers=HEADERS)
        print(f"Response status: {response.status_code}")

        if response.status_code != 200:
            print(f"HTTP error {response.status_code} while fetching chapters")
            return []

        html = BeautifulSoup(response.text, 'html.parser')

        chapter_list = html.find('div', class_='eplister', id='chapterlist')
        if not chapter_list:
            print("Could not find chapter list div.")
            return []

        chapter_items = chapter_list.find_all('li')
        if not chapter_items:
            print("No chapters found in the list.")
            return []

        chapters = []
        seen_urls = set()

        for item in chapter_items:
            link = item.find('a')
            if not link or not link.get('href'):
                continue

            chapter_url = link.get('href')
            if not chapter_url.startswith(('http://', 'https://')):
                chapter_url = urljoin(manga_url, chapter_url)

            if chapter_url in seen_urls:
                continue
            seen_urls.add(chapter_url)

            chapter_title_span = link.find('span', class_='chapternum')
            title = chapter_title_span.text.strip() if chapter_title_span else f"Chapter {item.get('data-num', '')}"

            chapters.append({'title': title, 'url': chapter_url})

        def get_chapter_number(chapter):
            try:
                return float(re.findall(r'(\d+\.?\d*)', chapter['title'])[-1])
            except (IndexError, ValueError):
                return float('-inf')

        chapters.sort(key=get_chapter_number)

        print(f"Found {len(chapters)} chapters.")
        return chapters

    except Exception as e:
        print(f"An error occurred while fetching chapters for ravenscans: {e}")
        return []


def fetch_chapter_images(chapter_url):
    resp = requests.get(chapter_url + '?style=list', headers=HEADERS)
    soup = BeautifulSoup(resp.text, 'html.parser')
    images = []
    # Find the reading-content div
    reading_content = soup.find('div', class_='reading-content')
    if reading_content:
        for img in reading_content.find_all('img'):
            src = img.get('data-lazy-src') or img.get('data-src') or img.get('src')
            if src:
                src = src.strip()
                if src.startswith('http') and src.split('?')[0].lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif')):
                    images.append(src)
    if not images:
        # Print the HTML for debugging
        print("\n--- DEBUG: No images found, here is the HTML snippet ---\n")
        print(resp.text[:5000])  # Print first 5000 chars for inspection
        print("\n--- END DEBUG ---\n")
    return images

def fetch_chapter_images_ravenscans(chapter_url):
    resp = requests.get(chapter_url, headers=HEADERS)
    soup = BeautifulSoup(resp.text, 'html.parser')
    images = []
    reader_area = soup.find('div', id='readerarea')
    if reader_area:
        for img in reader_area.find_all('img'):
            src = img.get('src')
            if src:
                src = src.strip()
                if src.startswith('http') and src.split('?')[0].lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif')):
                    images.append(src)
    return images

# --- Main workflow ---
def main():
    global BASE_URL, HEADERS # Declare that we are modifying global variables

    try:
        check_version()
    except VersionMismatchError as e:
        print(e) # Print the error message from the exception for CLI
        sys.exit(1)
    except requests.exceptions.RequestException as e: # Catch request errors if not caught by check_version's sys.exit
        print(f"Failed to check version: {e}")
        sys.exit(1)
    except Exception as e: # Catch any other unexpected error during version check for CLI
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)

    # Prompt user to select a source
    print("\\nPlease select a source:")
    selected_source_info = choose_from_list(AVAILABLE_SOURCES, "Select the source: ")
    
    # Update BASE_URL and Referer in HEADERS
    # Ensure BASE_URL does not have a trailing slash
    BASE_URL = selected_source_info['url'].rstrip('/')
    # Ensure Referer has a trailing slash
    HEADERS['Referer'] = selected_source_info['url'] if selected_source_info['url'].endswith('/') else selected_source_info['url'] + '/'
    
    print(f"Using source: {BASE_URL}")
    print(f"Referer set to: {HEADERS['Referer']}")

    if 'ravenscans.com' in BASE_URL:
        search_func = search_mangas_ravenscans
        chapters_func = fetch_chapters_ravenscans
        images_func = fetch_chapter_images_ravenscans
    else:
        search_func = search_mangas
        chapters_func = fetch_chapters
        images_func = fetch_chapter_images

    # 1. Prompt for manga title
    search_term = input("Enter manga title to search: ")

    # 2. Search for manga
    results = search_func(search_term)
    if not results:
        print("No manga found.")
        return
    manga = choose_from_list(results, "Select the correct manga: ")

    # 3. List chapters
    chapters = chapters_func(manga['url'])
    if not chapters:
        print("No chapters found.")
        return
    chapter = choose_from_list(chapters, "Select the chapter: ")

    # 4. Download chapter images
    images = images_func(chapter['url'])
    if not images:
        print("No images found for this chapter.")
        return
    chapter_folder = Path(f"{manga['title']} - {chapter['title']}")
    chapter_folder.mkdir(exist_ok=True)
    print(f"Downloading {len(images)} images to {chapter_folder}...")
    for idx, img_url in enumerate(images):
        ext = os.path.splitext(img_url)[1]
        img_path = chapter_folder / f"page_{idx+1}{ext}"
        with requests.get(img_url, stream=True, headers=HEADERS) as r:
            with open(img_path, 'wb') as f:
                shutil.copyfileobj(r.raw, f)

    # 5. OCR processing using MangaImageProcessor
    print("Running OCR split via MangaImageProcessor...")
    # Convert and split images for text extraction, separating text vs no-text
    convert_images_to_jpg_folder(str(chapter_folder))
    split_text_dir = chapter_folder / 'ocr_output'
    split_notext_dir = chapter_folder / 'ocr_no_text'
    split_text_dir.mkdir(exist_ok=True)
    split_notext_dir.mkdir(exist_ok=True)
    script_path = os.path.join(os.path.dirname(__file__), 'MangaImageProcessor.py')
    print(f"Invoking MangaImageProcessor for splitting: {script_path}")
    subprocess.run([
        sys.executable, script_path,
        '-i', str(chapter_folder.resolve()),
        '-o', str(split_text_dir.resolve()),
        '-o2', str(split_notext_dir.resolve()),
        '-m', 'ocr',
        '-l', 'en'
    ], check=True)
    # Extract and save text only from pages containing text
    print("Extracting text via Google Vision from text-only pages...")
    imgs = sorted([f for f in os.listdir(split_text_dir) if f.lower().endswith('.jpg')],
                key=lambda x: [int(c) if c.isdigit() else c for c in re.split(r'(\d+)', x)])
    txt_path = chapter_folder / f"Chapter {chapter['title']}.txt"
    with open(txt_path, 'w', encoding='utf-8') as outf:
        for img in imgs:
            txt = extract_text(str(split_text_dir / img))
            # Remove literal '\n' and internal newlines, convert to lowercase, strip extra spaces
            processed_txt = ' '.join(txt.replace('\\n', ' ').replace('\n', ' ').split()).lower()
            if processed_txt:
                outf.write(processed_txt + '\n')
    print(f"Saved OCR text to {txt_path}")

if __name__ == "__main__":
    main()

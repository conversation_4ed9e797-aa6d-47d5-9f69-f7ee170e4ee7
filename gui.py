import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import os
from pathlib import Path
import subprocess
import shutil
import sys
import re # For sorting image names
import requests # For downloading images
import traceback # Add this import if not already present
import autonomous_translate

# Attempt to import necessary components from autonomous_translate.py
try:
    from autonomous_translate import (
        search_mangas,
        fetch_chapters,
        fetch_chapter_images,
        search_mangas_ravenscans,
        fetch_chapters_ravenscans,
        fetch_chapter_images_ravenscans,
        convert_images_to_jpg_folder,
        extract_text,
        HEADERS as GLOBAL_HEADERS, # HTTP Headers for requests
        BASE_URL as GLOBAL_BASE_URL, # Base URL for requests
        AVAILABLE_SOURCES, # Available sources for manga
        client as vision_client, # Google Vision client
        check_version, # Import the version check function
        VersionMismatchError # Import the custom exception
    )
except ImportError as e:
    messagebox.showerror("Import Error", f"Could not import necessary components from autonomous_translate.py: {e}\nPlease ensure autonomous_translate.py is in the same directory and correctly structured.")
    sys.exit(1)
except AttributeError as e:
    messagebox.showerror("Attribute Error", f"A required component (like HEADERS or client) might be missing or not top-level in autonomous_translate.py: {e}")
    sys.exit(1)


ctk.set_appearance_mode("Dark")
ctk.set_default_color_theme("blue")

class MangaTranslatorGUI(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.title("Manga Translator GUI")
        self.geometry("1000x800")

        self.grid_columnconfigure(0, weight=1)
        # self.grid_rowconfigure(5, weight=1) # Will be set after all frames are gridded, for row 5 (output_main_frame)

        # --- Output Main Frame (Create first to ensure log_textbox exists for on_source_selected)---
        self.output_main_frame = ctk.CTkFrame(self)
        # DO NOT GRID output_main_frame YET. It will be gridded at row 4 later.
        self.output_main_frame.grid_columnconfigure(0, weight=1) # Configure internal grid
        self.output_main_frame.grid_rowconfigure(0, weight=1)  # Configure internal grid for notebook

        self.output_notebook = ctk.CTkTabview(self.output_main_frame)
        self.output_notebook.pack(expand=True, fill="both", padx=5, pady=5)
        self.output_notebook.add("Logs")
        self.output_notebook.add("OCR Result")

        self.log_textbox = ctk.CTkTextbox(self.output_notebook.tab("Logs"), wrap="word", state="disabled", font=("Consolas", 10))
        self.log_textbox.pack(expand=True, fill="both", padx=5, pady=5)

        self.ocr_result_textbox = ctk.CTkTextbox(self.output_notebook.tab("OCR Result"), wrap="word", state="normal", font=("Consolas", 10))
        self.ocr_result_textbox.pack(expand=True, fill="both", padx=5, pady=5)

        # --- Source Selection Frame ---
        self.source_frame = ctk.CTkFrame(self)
        self.source_frame.grid(row=0, column=0, padx=10, pady=(10,0), sticky="ew")
        self.source_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(self.source_frame, text="Select Source:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.source_options = [source['title'] for source in AVAILABLE_SOURCES]
        self.selected_source_var = ctk.StringVar(value=self.source_options[0] if self.source_options else "")
        self.source_menu = ctk.CTkOptionMenu(self.source_frame, variable=self.selected_source_var, values=self.source_options, command=self.on_source_selected)
        self.source_menu.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        self.test_connection_button = ctk.CTkButton(self.source_frame, text="Test Connection", command=self.test_connection_thread)
        self.test_connection_button.grid(row=0, column=2, padx=5, pady=5)

        # --- Top Frame for Search ---
        self.search_frame = ctk.CTkFrame(self)
        self.search_frame.grid(row=1, column=0, padx=10, pady=10, sticky="ew")
        self.search_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(self.search_frame, text="Manga Title:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.title_entry = ctk.CTkEntry(self.search_frame, placeholder_text="Enter manga title")
        self.title_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.search_button = ctk.CTkButton(self.search_frame, text="Search", command=self.search_manga_thread)
        self.search_button.grid(row=0, column=2, padx=5, pady=5)

        # --- Middle Frame for Results ---
        self.results_frame = ctk.CTkFrame(self)
        self.results_frame.grid(row=2, column=0, padx=10, pady=5, sticky="nsew")
        self.results_frame.grid_columnconfigure(0, weight=1)
        self.results_frame.grid_columnconfigure(1, weight=1)
        self.results_frame.grid_rowconfigure(1, weight=1)

        ctk.CTkLabel(self.results_frame, text="Search Results:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.manga_results_listbox = tk.Listbox(self.results_frame, bg="#2B2B2B", fg="white", selectbackground="#1F6AA5", relief="sunken", borderwidth=1, exportselection=False, activestyle="none")
        self.manga_results_listbox.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        self.manga_results_listbox.bind("<<ListboxSelect>>", self.on_manga_select)
        self.manga_results_data = []

        ctk.CTkLabel(self.results_frame, text="Chapters:").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        self.chapter_listbox = tk.Listbox(self.results_frame, bg="#2B2B2B", fg="white", selectbackground="#1F6AA5", relief="sunken", borderwidth=1, exportselection=False, activestyle="none")
        self.chapter_listbox.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
        self.chapter_listbox.bind("<<ListboxSelect>>", self.on_chapter_select)
        self.chapter_data = []

        self.process_button = ctk.CTkButton(self, text="Download and OCR Selected Chapter", command=self.process_chapter_thread, state="disabled")
        self.process_button.grid(row=3, column=0, padx=10, pady=(10,0), sticky="ew")

        # --- Process All Checkbox ---
        self.process_all_var = ctk.BooleanVar(value=False)
        self.process_all_checkbox = ctk.CTkCheckBox(self, text="Process all subsequent chapters", variable=self.process_all_var)
        self.process_all_checkbox.grid(row=4, column=0, padx=10, pady=(0,10), sticky="w")
        
        # Grid the output_main_frame now, in its designated row
        self.output_main_frame.grid(row=5, column=0, padx=10, pady=10, sticky="nsew")
        
        # Configure the main window\'s row that contains the output_main_frame to expand
        self.grid_rowconfigure(5, weight=1) # Row 5 is for output_main_frame

        # --- Add Images from Local Folder Button ---
        self.add_local_folder_button = ctk.CTkButton(self, text="Add Images from Folder", command=self.add_images_from_folder)
        self.add_local_folder_button.grid(row=6, column=0, padx=10, pady=(0,10), sticky="ew")


        self.selected_manga = None
        self.selected_chapter = None

        self.search_func = None
        self.chapters_func = None
        self.images_func = None

        # Call after all widgets are created
        self.on_source_selected(self.selected_source_var.get()) # Initialize BASE_URL and HEADERS. Now safe.

    def on_source_selected(self, selected_title):
        selected_source_info = next((s for s in AVAILABLE_SOURCES if s['title'] == selected_title), None)
        if selected_source_info:
            autonomous_translate.BASE_URL = selected_source_info['url'].rstrip('/')
            autonomous_translate.HEADERS['Referer'] = selected_source_info['url'] if selected_source_info['url'].endswith('/') else selected_source_info['url'] + '/'
            
            self.log_message(f"Source selected: {autonomous_translate.BASE_URL}")
            self.log_message(f"Referer set to: {autonomous_translate.HEADERS['Referer']}")

            if 'ravenscans.com' in autonomous_translate.BASE_URL:
                self.search_func = search_mangas_ravenscans
                self.chapters_func = fetch_chapters_ravenscans
                self.images_func = fetch_chapter_images_ravenscans
                self.title_entry.configure(placeholder_text="Enter Manga URL")
                self.search_button.configure(text="Fetch Chapters", command=self.fetch_chapters_from_url_thread)
            else:
                self.search_func = search_mangas
                self.chapters_func = fetch_chapters
                self.images_func = fetch_chapter_images
                self.title_entry.configure(placeholder_text="Enter manga title")
                self.search_button.configure(text="Search", command=self.search_manga_thread)

            # Reset search and chapter lists when source changes
            self.title_entry.delete(0, tk.END)
            self.manga_results_listbox.delete(0, tk.END)
            self.chapter_listbox.delete(0, tk.END)
            self.manga_results_data = []
            self.chapter_data = []
            self.selected_manga = None
            self.selected_chapter = None
            self.process_button.configure(state="disabled")
            self.search_button.configure(state="normal")


    def test_connection_thread(self):
        if not autonomous_translate.BASE_URL:
            messagebox.showerror("Error", "Please select a source first.")
            return
        self.log_message(f"Testing connection to {autonomous_translate.BASE_URL}...")
        self.test_connection_button.configure(state="disabled")
        threading.Thread(target=self.test_connection_worker, daemon=True).start()

    def test_connection_worker(self):
        try:
            response = requests.get(autonomous_translate.BASE_URL, headers=autonomous_translate.HEADERS, timeout=10)
            response.raise_for_status()  # Raises an exception for 4XX/5XX errors
            self.log_message(f"Successfully connected to {autonomous_translate.BASE_URL} (Status: {response.status_code}).")
            messagebox.showinfo("Connection Test", f"Successfully connected to {autonomous_translate.BASE_URL}.")
        except requests.exceptions.RequestException as e:
            self.log_message(f"Failed to connect to {autonomous_translate.BASE_URL}: {e}")
            messagebox.showerror("Connection Test", f"Failed to connect to {autonomous_translate.BASE_URL}.\n\nError: {e}")
        finally:
            self.test_connection_button.configure(state="normal")

    def log_message(self, message):
        self.log_textbox.configure(state="normal")
        self.log_textbox.insert(tk.END, message + "\n")
        self.log_textbox.configure(state="disabled")
        self.log_textbox.see(tk.END)
        self.update_idletasks()

    def fetch_chapters_from_url_thread(self):
        manga_url = self.title_entry.get()
        if not manga_url or not ('http://' in manga_url or 'https://' in manga_url):
            messagebox.showerror("Error", "Please enter a valid manga URL.")
            return

        self.log_message(f"Fetching chapters directly from URL: {manga_url}")

        try:
            manga_title = manga_url.strip('/').split('/')[-1].replace('-', ' ').title()
        except:
            manga_title = "Manga from URL"

        self.selected_manga = {'title': manga_title, 'url': manga_url}
        self.manga_results_data = [self.selected_manga]
        self.manga_results_listbox.delete(0, tk.END)
        self.manga_results_listbox.insert(tk.END, f"1. {self.selected_manga['title']}")
        self.manga_results_listbox.selection_set(0)

        self.log_message(f"Selected manga: {self.selected_manga['title']}. Fetching chapters...")
        self.chapter_listbox.delete(0, tk.END)
        self.chapter_data = []
        self.process_button.configure(state="disabled")
        threading.Thread(target=self.fetch_chapters_worker, args=(self.selected_manga['url'],), daemon=True).start()

    def search_manga_thread(self):
        title = self.title_entry.get()
        if not title:
            messagebox.showerror("Error", "Please enter a manga title.")
            return
        
        # Ensure source is selected and globals are set
        if not autonomous_translate.BASE_URL:
            messagebox.showerror("Error", "Please select a source first.")
            self.log_message("Error: No source selected for search.")
            return

        self.log_message(f"Searching for manga: {title} on {autonomous_translate.BASE_URL}...")
        self.search_button.configure(state="disabled")
        self.manga_results_listbox.delete(0, tk.END)
        self.chapter_listbox.delete(0, tk.END)
        self.manga_results_data = []
        self.chapter_data = []
        self.process_button.configure(state="disabled")
        threading.Thread(target=self.search_manga_worker, args=(title,), daemon=True).start()

    def search_manga_worker(self, title):
        try:
            # search_mangas will use the globally set BASE_URL and HEADERS
            results = self.search_func(title)
            if not results:
                self.log_message("No manga found.")
                messagebox.showinfo("Search Results", "No manga found.")
            else:
                self.log_message(f"Found {len(results)} manga(s).")
                self.manga_results_data = results
                for i, manga in enumerate(results):
                    self.manga_results_listbox.insert(tk.END, f"{i+1}. {manga['title']}")
        except Exception as e:
            self.log_message(f"Error during manga search: {e}")
            messagebox.showerror("Error", f"Failed to search manga: {e}")
        finally:
            self.search_button.configure(state="normal")

    def on_manga_select(self, event):
        selection = self.manga_results_listbox.curselection()
        if not selection: return
        self.selected_manga = self.manga_results_data[selection[0]]
        self.log_message(f"Selected manga: {self.selected_manga['title']}. Fetching chapters from {autonomous_translate.BASE_URL}...")
        self.chapter_listbox.delete(0, tk.END)
        self.chapter_data = []
        self.process_button.configure(state="disabled")
        threading.Thread(target=self.fetch_chapters_worker, args=(self.selected_manga['url'],), daemon=True).start()

    def fetch_chapters_worker(self, manga_url):
        try:
            # fetch_chapters will use the globally set BASE_URL and HEADERS
            chapters = self.chapters_func(manga_url)
            if not chapters:
                self.log_message("No chapters found for this manga.")
                messagebox.showinfo("Chapters", "No chapters found.")
            else:
                self.log_message(f"Found {len(chapters)} chapter(s).")
                self.chapter_data = chapters
                for i, chap in enumerate(chapters):
                    self.chapter_listbox.insert(tk.END, f"{i+1}. {chap['title']}")
        except Exception as e:
            self.log_message(f"Error fetching chapters: {e}")
            messagebox.showerror("Error", f"Failed to fetch chapters: {e}")

    def on_chapter_select(self, event):
        selection = self.chapter_listbox.curselection()
        if not selection: return
        self.selected_chapter = self.chapter_data[selection[0]]
        self.log_message(f"Selected chapter: {self.selected_chapter['title']}")
        self.process_button.configure(state="normal")

    def process_chapter_thread(self):
        if not self.selected_manga or not self.selected_chapter:
            messagebox.showerror("Error", "Please select a manga and a chapter.")
            return
        
        # Log initial chapter differently if processing all
        if self.process_all_var.get():
            self.log_message(f"Starting automatic processing for: {self.selected_manga['title']} - from chapter {self.selected_chapter['title']}...")
        else:
            self.log_message(f"Starting process for: {self.selected_manga['title']} - {self.selected_chapter['title']}")
        
        self.process_button.configure(state="disabled")
        self.search_button.configure(state="disabled")
        self.process_all_checkbox.configure(state="disabled") # Disable checkbox during processing
        
        # Pass the initial selected chapter and manga info to the worker
        initial_chapter_index = self.chapter_listbox.curselection()[0] # Assumes a chapter is selected
        threading.Thread(target=self.process_chapters_loop_worker, 
                         args=(self.selected_manga, self.chapter_data, initial_chapter_index, self.process_all_var.get()), 
                         daemon=True).start()

    def _get_processor_script_path(self):
        # Assuming MangaImageProcessor.py is in the same directory as this script (gui.py)
        # or the executable.
        if getattr(sys, 'frozen', False):
            # If the application is run as a bundle/executable
            application_path = Path(sys.executable).parent
        else:
            # If run as a script
            application_path = Path(__file__).resolve().parent
        
        # Given the workspace structure, MangaImageProcessor.py is in the root,
        # which is the same directory as gui.py.
        script_path = application_path / "MangaImageProcessor.py"
        # self.log_message(f"Processor script path determined to be: {script_path}") # Optional debug log
        return script_path

    def _execute_single_chapter_processing(self, manga, chapter):
        # This method contains the core logic for processing ONE chapter.
        # It's extracted from the original process_chapter_worker
        # Returns True on success, False on failure.
        try:
            self.log_message(f"Processing: {manga['title']} - {chapter['title']}")
            self.log_message(f"Fetching image URLs for {chapter['title']} from {autonomous_translate.BASE_URL}...")
            images_urls = self.images_func(chapter['url'])
            if not images_urls:
                self.log_message(f"No images found for chapter {chapter['title']}.")
                messagebox.showinfo("Error", f"No images found for {chapter['title']}.")
                return False

            chapter_folder_name = f"{manga['title']} - {chapter['title']}"
            chapter_folder = Path(chapter_folder_name)
            chapter_folder.mkdir(exist_ok=True)
            self.log_message(f"Downloading {len(images_urls)} images to {chapter_folder}...")

            for idx, img_url in enumerate(images_urls):
                ext = os.path.splitext(img_url)[1].split('?')[0]
                if not ext or ext.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                    try:
                        content_type = requests.head(img_url, headers=autonomous_translate.HEADERS, timeout=10).headers.get('content-type', '').lower()
                        if 'jpeg' in content_type or 'jpg' in content_type: ext = '.jpg'
                        elif 'png' in content_type: ext = '.png'
                        elif 'webp' in content_type: ext = '.webp'
                        elif 'gif' in content_type: ext = '.gif'
                        else: ext = '.jpg'
                    except requests.exceptions.RequestException:
                        ext = '.jpg'
                
                img_path = chapter_folder / f"page_{idx+1}{ext}"
                self.log_message(f"DL: {img_url} -> {img_path} ({idx+1}/{len(images_urls)})")
                try:
                    with requests.get(img_url, stream=True, headers=autonomous_translate.HEADERS, timeout=30) as r:
                        r.raise_for_status()
                        with open(img_path, 'wb') as f: shutil.copyfileobj(r.raw, f)
                except requests.exceptions.RequestException as e_req:
                    self.log_message(f"Failed to download {img_url}: {e_req}")
                    # Decide if we should stop all processing or just skip this image
                    # For now, let's skip and try to continue with the chapter
                    continue 

            self.log_message("Image download complete.")
            self.log_message("Converting images to JPG for OCR...")
            convert_images_to_jpg_folder(str(chapter_folder))

            self.log_message("Running OCR split via MangaImageProcessor...")
            split_text_dir = chapter_folder / 'ocr_output'
            split_notext_dir = chapter_folder / 'ocr_no_text'
            split_text_dir.mkdir(exist_ok=True)
            split_notext_dir.mkdir(exist_ok=True)
            processor_script_path = self._get_processor_script_path()
            if not processor_script_path.exists():
                self.log_message(f"Error: MangaImageProcessor.py not found.")
                messagebox.showerror("Error", "MangaImageProcessor.py not found.")
                return False

            run_args = [
                sys.executable, str(processor_script_path),
                '--input', str(chapter_folder.resolve()),
                '--output', str(split_text_dir.resolve()),
                '--output2', str(split_notext_dir.resolve()),
                '--mode', 'ocr', '-l', 'en'
            ]
            self.log_message(f"Running command: {' '.join(run_args)}")
            result = subprocess.run(run_args, check=False, capture_output=True, text=True, encoding='utf-8')
            if result.returncode != 0:
                self.log_message(f"MangaImageProcessor Error (Code {result.returncode}):\nStdout:\n{result.stdout}\nStderr:\n{result.stderr}")
                # Do not raise, just log and return False to stop further processing if in a loop
                messagebox.showerror("MangaImageProcessor Error", f"Error processing {chapter['title']}. Check logs.")
                return False
            self.log_message(f"MangaImageProcessor Stdout:\n{result.stdout}")
            if result.stderr: self.log_message(f"MangaImageProcessor Stderr:\n{result.stderr}")
            self.log_message("Image splitting complete.")

            self.log_message("Extracting text via Google Vision...")
            imgs_to_ocr = sorted([f for f in os.listdir(split_text_dir) if f.lower().endswith('.jpg')], 
                                 key=lambda x: [int(c) if c.isdigit() else c for c in re.split(r'(\d+)', x)])
            if not imgs_to_ocr: 
                self.log_message(f"No .jpg images found in {split_text_dir} to OCR for {chapter['title']}.")
                # This might not be a fatal error for the whole batch, but the chapter won't have text.
                # Let's consider it a partial success for this chapter if other steps were okay.
                # However, if no images were downloaded at all, it's a bigger issue.
                # For now, if no images to OCR, we'll say this chapter processing is "done" but perhaps not "successful" in terms of text output.
                # To simplify, let's return True but the text file will be empty or indicate no text.
                final_text_path_empty = chapter_folder / f"Chapter {chapter['title']}.txt"
                with open(final_text_path_empty, 'w', encoding='utf-8') as outf_empty:
                    outf_empty.write("[No text found or no images to OCR]\n\n")
                self.ocr_result_textbox.delete("1.0", tk.END)
                self.ocr_result_textbox.insert(tk.END, "[No text found or no images to OCR]")
                self.output_notebook.set("OCR Result")   

                return True # Chapter processed, but no text

            final_text_path = chapter_folder / f"Chapter {chapter['title']}.txt"
            all_extracted_text = []
            with open(final_text_path, 'w', encoding='utf-8') as outf:
                for i, img_name in enumerate(imgs_to_ocr):
                    img_full_path = split_text_dir / img_name
                    self.log_message(f"OCR: {img_name} ({i+1}/{len(imgs_to_ocr)}) for {chapter['title']}")
                    try:
                        if vision_client is None: raise Exception("Google Vision client not initialized.")
                        text = extract_text(str(img_full_path))
                        if text:
                            # Process text: remove newlines, collapse spaces, convert to lowercase
                            processed_txt = ' '.join(text.replace('\\n', ' ').replace('\n', ' ').split()).lower()
                            outf.write(processed_txt + '\n\n')  # Blank line between images
                            all_extracted_text.append(processed_txt)
                        else:
                            self.log_message(f"No text in {img_name}.")
                            # No text detected, write blank line
                            outf.write('\n')
                            all_extracted_text.append("")
                    except Exception as e_ocr:
                        self.log_message(f"OCR Error for {img_name} in {chapter['title']}: {e_ocr}")
                        outf.write(f"[OCR Error for {img_name}]\n\n"); all_extracted_text.append(f"[OCR Error for {img_name}]")
            
            self.log_message(f"Saved OCR text for {chapter['title']} to {final_text_path}")
            if not self.process_all_var.get(): # Only show popup if not in batch mode, or for the last one
                 messagebox.showinfo("Success", f"Process complete for {chapter['title']}! OCR text saved to {final_text_path}")
            self.ocr_result_textbox.delete("1.0", tk.END)
            # Display each bubble on its own line
            self.ocr_result_textbox.insert(tk.END, "\n\n".join(all_extracted_text))  # Blank line between images
            self.output_notebook.set("OCR Result")
            return True

        except subprocess.CalledProcessError as e_sub: # Should be caught by the check above, but as a fallback
            self.log_message(f"Subprocess Error during {chapter['title']}: {e_sub}\nStdout: {e_sub.stdout}\nStderr: {e_sub.stderr}")
            messagebox.showerror("Subprocess Error", f"A subprocess failed for {chapter['title']}: {e_sub.stderr or e_sub.stdout or e_sub}")
            return False
        except Exception as e:
            self.log_message(f"An error occurred processing {chapter['title']}: {e}")
            import traceback
            self.log_message(traceback.format_exc())
            messagebox.showerror("Error", f"An unexpected error for {chapter['title']}: {e}")
            return False

    def process_chapters_loop_worker(self, manga, all_chapters_data, initial_chapter_idx, process_all_enabled):
        try:
            if process_all_enabled:
                for i in range(initial_chapter_idx, len(all_chapters_data)):
                    current_chapter_data = all_chapters_data[i]
                    self.selected_chapter = current_chapter_data # Update internal state
                    
                    # Update UI to reflect current chapter being processed
                    self.chapter_listbox.selection_clear(0, tk.END)
                    self.chapter_listbox.selection_set(i)
                    self.chapter_listbox.activate(i)
                    self.chapter_listbox.see(i)
                    self.update_idletasks() # Force UI update

                    self.log_message(f"--- Starting automatic processing for chapter: {current_chapter_data['title']} ({i+1-initial_chapter_idx}/{len(all_chapters_data)-initial_chapter_idx}) ---")
                    success = self._execute_single_chapter_processing(manga, current_chapter_data)
                    if not success:
                        self.log_message(f"Automatic processing stopped due to error in chapter {current_chapter_data['title']}.")
                        messagebox.showerror("Processing Stopped", f"Error in chapter {current_chapter_data['title']}. Automatic processing halted.")
                        break # Stop processing further chapters
                    self.log_message(f"--- Finished chapter: {current_chapter_data['title']} ---")
                self.log_message("All selected subsequent chapters processed.")
                messagebox.showinfo("Batch Complete", "Finished processing all selected chapters.")
            else:
                # Process only the initially selected chapter
                current_chapter_data = all_chapters_data[initial_chapter_idx]
                self.selected_chapter = current_chapter_data # Ensure it's set
                self._execute_single_chapter_processing(manga, current_chapter_data)
        finally:
            self._enable_buttons_after_processing()

    def _get_processor_script_path(self):
        # Assuming MangaImageProcessor.py is in the same directory as this script (gui.py)
        # or the executable.
        if getattr(sys, 'frozen', False):
            # If the application is run as a bundle/executable
            application_path = Path(sys.executable).parent
        else:
            # If run as a script
            application_path = Path(__file__).resolve().parent
        
        # Given the workspace structure, MangaImageProcessor.py is in the root,
        # which is the same directory as gui.py.
        script_path = application_path / "MangaImageProcessor.py"
        # self.log_message(f"Processor script path determined to be: {script_path}") # Optional debug log
        return script_path

    def _enable_buttons_after_processing(self):
        self.process_button.configure(state="normal" if self.selected_chapter else "disabled")
        self.search_button.configure(state="normal")
        self.process_all_checkbox.configure(state="normal") # Re-enable checkbox

    def add_images_from_folder(self):
        folder_path_str = filedialog.askdirectory(title="Select Folder Containing Images")
        if not folder_path_str:
            self.log_message("No folder selected.")
            return
        
        self.log_message(f"Starting to process local folder: {folder_path_str}")
        
        self.add_local_folder_button.configure(state="disabled")
        # Consider disabling other buttons if needed to prevent concurrent operations
        # self.search_button.configure(state="disabled")
        # self.process_button.configure(state="disabled")

        threading.Thread(target=self._process_local_folder_thread_entry, args=(folder_path_str,), daemon=True).start()

    def _process_local_folder_thread_entry(self, folder_path_str):
        try:
            self._process_local_folder_worker(folder_path_str)
        except Exception as e:
            self.log_message(f"Critical error in local folder processing thread: {e}")
            self.log_message(traceback.format_exc())
            messagebox.showerror("Critical Error", f"A critical error occurred: {e}")
        finally:
            self.add_local_folder_button.configure(state="normal")
            # Re-enable other buttons if they were disabled
            # self.search_button.configure(state="normal")
            # self.process_button.configure(state="normal" if self.selected_chapter else "disabled")


    def _process_local_folder_worker(self, local_folder_path_str):
        folder_path = Path(local_folder_path_str)
        folder_name = folder_path.name

        try:
            self.log_message(f"Processing images in folder: {folder_name}")

            self.log_message(f"Converting images to JPG in '{folder_name}' for OCR...")
            convert_images_to_jpg_folder(str(folder_path)) # From autonomous_translate.py

            self.log_message(f"Running OCR split via MangaImageProcessor for '{folder_name}'...")
            split_text_dir = folder_path / 'ocr_output'
            split_notext_dir = folder_path / 'ocr_no_text'
            split_text_dir.mkdir(exist_ok=True)
            split_notext_dir.mkdir(exist_ok=True)
            
            processor_script_path = self._get_processor_script_path() # Assumes this method exists and is correct
            if not processor_script_path or not processor_script_path.exists():
                self.log_message(f"Error: MangaImageProcessor.py not found at expected path: {processor_script_path}")
                messagebox.showerror("Error", f"MangaImageProcessor.py not found. Expected at: {processor_script_path}")
                return False

            run_args = [
                sys.executable, str(processor_script_path),
                '--input', str(folder_path.resolve()),
                '--output', str(split_text_dir.resolve()),
                '--output2', str(split_notext_dir.resolve()),
                '--mode', 'ocr', '-l', 'en' 
            ]
            self.log_message(f"Running command: {' '.join(run_args)}")
            result = subprocess.run(run_args, check=False, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode != 0:
                self.log_message(f"MangaImageProcessor Error for '{folder_name}' (Code {result.returncode}):\nStdout:\n{result.stdout}\nStderr:\n{result.stderr}")
                messagebox.showerror("MangaImageProcessor Error", f"Error processing '{folder_name}'. Check logs.")
                return False # Indicate failure
            self.log_message(f"MangaImageProcessor Stdout for '{folder_name}':\n{result.stdout or '(No stdout)'}")
            if result.stderr: self.log_message(f"MangaImageProcessor Stderr for '{folder_name}':\n{result.stderr}")
            self.log_message(f"Image splitting complete for '{folder_name}'.")

            self.log_message(f"Extracting text via Google Vision for '{folder_name}'...")
            imgs_to_ocr = sorted(
                [f for f in os.listdir(split_text_dir) if f.lower().endswith(('.jpg', '.jpeg'))],
                key=lambda x: [int(c) if c.isdigit() else c for c in re.split(r'(\d+)', x)]
            )

            if not imgs_to_ocr:
                self.log_message(f"No .jpg/.jpeg images found in '{split_text_dir}' to OCR for '{folder_name}'.")
                final_text_path_empty = folder_path / f"{folder_name} - OCR Output.txt"
                with open(final_text_path_empty, 'w', encoding='utf-8') as outf_empty:
                    outf_empty.write(f"[No text found or no JPG/JPEG images to OCR in '{split_text_dir.name}' folder for {folder_name}]\\n\\n")
                self.ocr_result_textbox.delete("1.0", tk.END)
                self.ocr_result_textbox.insert(tk.END, f"[No text found or no JPG/JPEG images to OCR in '{split_text_dir.name}' folder for {folder_name}]")
                self.output_notebook.set("OCR Result")
                messagebox.showinfo("Info", f"No JPG/JPEG images found to OCR in the '{split_text_dir.name}' subfolder of '{folder_name}'.")
                return True # Processed, but no text extracted from this step

            final_text_path = folder_path / f"{folder_name} - OCR Output.txt"
            all_extracted_text = []
            with open(final_text_path, 'w', encoding='utf-8') as outf:
                for i, img_name in enumerate(imgs_to_ocr):
                    img_full_path = split_text_dir / img_name
                    self.log_message(f"OCR: {img_name} ({i+1}/{len(imgs_to_ocr)}) for '{folder_name}'")
                    try:
                        if vision_client is None: 
                            self.log_message("Error: Google Vision client not initialized.")
                            raise Exception("Google Vision client not initialized.")
                        text = extract_text(str(img_full_path)) # From autonomous_translate.py
                        if text:
                            # Process text: remove newlines, collapse spaces, convert to lowercase
                            processed_txt = ' '.join(text.replace('\\n', ' ').replace('\n', ' ').split()).lower()
                            outf.write(processed_txt + '\n\n')  # Blank line between images
                            all_extracted_text.append(processed_txt)
                        else:
                            self.log_message(f"No text in {img_name}.")
                            # No text detected, write blank line
                            outf.write('\n')
                            all_extracted_text.append("[No text detected in this image segment]")
                    except Exception as e_ocr:
                        self.log_message(f"OCR Error for {img_name} in '{folder_name}': {e_ocr}")
                        outf.write(f"[OCR Error for {img_name}: {e_ocr}]\\n\\n"); all_extracted_text.append(f"[OCR Error for {img_name}]")
            
            self.log_message(f"Saved OCR text for '{folder_name}' to {final_text_path}")
            messagebox.showinfo("Success", f"Process complete for '{folder_name}'! OCR text saved to {final_text_path}")
            self.ocr_result_textbox.delete("1.0", tk.END)
            # Display each bubble on its own line
            self.ocr_result_textbox.insert(tk.END, "\n\n".join(all_extracted_text))  # Blank line between images
            self.output_notebook.set("OCR Result")
            return True # Success

        except subprocess.CalledProcessError as e_sub: # Should be caught by the check above, but as a fallback
            self.log_message(f"Subprocess Error during processing of '{folder_name}': {e_sub}\\nStdout: {e_sub.stdout}\\nStderr: {e_sub.stderr}")
            messagebox.showerror("Subprocess Error", f"A subprocess failed for '{folder_name}': {e_sub.stderr or e_sub.stdout or e_sub}")
            return False
        except Exception as e:
            self.log_message(f"An error occurred processing '{folder_name}': {e}")
            self.log_message(traceback.format_exc()) # Make sure traceback is imported
            messagebox.showerror("Error", f"An unexpected error occurred for '{folder_name}': {e}")
            return False

    def test_connection_thread(self):
        if not GLOBAL_BASE_URL:
            messagebox.showerror("Error", "Please select a source first.")
            return
        self.log_message(f"Testing connection to {GLOBAL_BASE_URL}...")
        self.test_connection_button.configure(state="disabled")
        threading.Thread(target=self.test_connection_worker, daemon=True).start()

    def test_connection_worker(self):
        try:
            response = requests.get(GLOBAL_BASE_URL, headers=GLOBAL_HEADERS, timeout=10)
            response.raise_for_status()  # Raises an exception for 4XX/5XX errors
            self.log_message(f"Successfully connected to {GLOBAL_BASE_URL} (Status: {response.status_code}).")
            messagebox.showinfo("Connection Test", f"Successfully connected to {GLOBAL_BASE_URL}.")
        except requests.exceptions.RequestException as e:
            self.log_message(f"Failed to connect to {GLOBAL_BASE_URL}: {e}")
            messagebox.showerror("Connection Test", f"Failed to connect to {GLOBAL_BASE_URL}.\n\nError: {e}")
        finally:
            self.test_connection_button.configure(state="normal")

if __name__ == "__main__":
    try:
        check_version() # Call version check before starting GUI
    except VersionMismatchError as e: # Catch the custom exception
        messagebox.showerror("Version Mismatch", str(e))
        sys.exit(1)
    except SystemExit: # Catch SystemExit if version check fails for other reasons (e.g. request error)
        # The message would have been printed by autonomous_translate.py, or we can show a generic one
        # messagebox.showerror("Version Check Failed", "Could not verify application version.")
        sys.exit(1) # Re-raise to ensure script terminates
    except Exception as e: # Catch any other unexpected error during version check
        messagebox.showerror("Version Check Error", f"An unexpected error occurred during version check: {e}")
        sys.exit(1)

    # Removed check for local credentials.json as it's fetched from URL now
    if vision_client is None: # Check if client initialized properly in autonomous_translate
        messagebox.showerror("Vision Client Error", "Google Vision Client could not be initialized. Check autonomous_translate.py.")
        sys.exit(1)
        
    app = MangaTranslatorGUI()
    app.mainloop()
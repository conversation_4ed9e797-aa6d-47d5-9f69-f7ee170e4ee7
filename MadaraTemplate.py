from abc import ABC
import requests
import json
import re
import httpx
import base64
from scrapers.base_scraper import BaseScraper
from scrapers.common_headers import CommonHeaders
from bs4 import BeautifulSoup
from utils.request_manager import RequestManager
from lib.cryptoaes import CryptoAES
from scrapers.MangaScraperTemplate import MangaScraperTemplate
import logging

logger = logging.getLogger(__name__)

class MadaraTemplate(MangaScraperTemplate):
    DOMAIN = None
    BASE_URL = None
    VALID_FIND = {
        "1": "Par recherche",
        "2": "Par liste de series",
        "3": "Par url"
    }
    
    def __init__(self):
        if not self.BASE_URL or not self.DOMAIN:  
            raise ValueError('Les paramètres de configuration ne peuvent pas être None.')
        self.request_manager = RequestManager(self.BASE_URL)

    
    def decrypt_chapter_data(self, chapter_data_str, password):
        chapter_data = json.loads(chapter_data_str)
        logger.debug("Décryptage des données du chapitre")
        salted = b'Salted__'
        salt = bytes.fromhex(chapter_data['s'])
        unsalted_ciphertext = base64.b64decode(chapter_data['ct'])
        ciphertext = salted + salt + unsalted_ciphertext
        decrypted_text = CryptoAES.decrypt(base64.b64encode(ciphertext), password)
        return decrypted_text

    
    def fetch_manga_data(self, manga_url):
        response = self.request_manager.get(manga_url)
        html = BeautifulSoup(response.text, 'html.parser')
        return {
            'title': html.find('div', {'class': 'post-title'}).find('h1').text,
            'url': manga_url }

    
    def fetch_chapter_images(self, chapter_url):
        """Fetch images from a chapter page.
        
        Args:
            chapter_url (str): URL of the chapter
            
        Returns:
            list: List of image URLs
        """
        try:
            logger.debug(f"Récupération des images pour le chapitre {chapter_url}")
            response = self.request_manager.get(chapter_url + '?style=list')
            html = BeautifulSoup(response.text, 'html.parser')
            images = []
            
            # 1. Try to get protected images first
            protector_data = html.find('chapter-protector-data', {'id': True})
            if protector_data and protector_data.string:
                try:
                    password = re.search("wpmangaprotectornonce='(.*?)';", protector_data.string).group(1)
                    chapter_data_str = re.search("chapter_data='(.*?)';", protector_data.string).group(1).replace('\\/', '/')
                    decrypted_text = json.loads(self.decrypt_chapter_data(chapter_data_str, password))
                    images_data = json.loads(decrypted_text)
                    if isinstance(images_data, list):
                        for img in images_data:
                            if isinstance(img, dict) and 'src' in img:
                                images.append(img['src'])
                            elif isinstance(img, str):
                                images.append(img)
                except (AttributeError, json.JSONDecodeError) as e:
                    logger.error(f"Erreur lors du déchiffrement des images protégées: {str(e)}")
            
            # 2. If no protected images found, try regular Madara containers
            if not images:
                containers = []
                containers.extend(html.find_all('div', class_=['reading-content', 'page-break']))
                containers.extend(html.find_all('div', id=['manga-reading-nav-head', 'manga-reading-nav-foot']))
                
                for container in containers:
                    for img in container.find_all('img', {'class': ['wp-manga-chapter-img', 'wp-manga-chapter-image']}):
                        src = (
                            img.get('data-lazy-src') or 
                            img.get('data-src') or 
                            img.get('src', '')
                        )
                        if src and src not in images:
                            images.append(src)
            
            # 3. If still no images, try the MangaScraperTemplate method
            if not images:
                return super().fetch_chapter_images(chapter_url)
            
            # Clean up URLs
            cleaned_images = []
            for img_url in images:
                if img_url:
                    # Clean up URL
                    img_url = (img_url
                             .replace('http://', 'https://')
                             .replace('\n', '')
                             .strip())
                    
                    # Make URL absolute if needed
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = self.BASE_URL + ('' if img_url.startswith('/') else '/') + img_url
                    
                    # Only add if it's a valid image URL
                    if img_url.endswith(('.jpg', '.jpeg', '.png', '.webp', '.gif')):
                        cleaned_images.append(img_url)
            
            if not cleaned_images:
                logger.error(f"Aucune image trouvée dans le chapitre: {chapter_url}")
                logger.debug(f"HTML reçu: {response.text}")
                return []
            
            return cleaned_images
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des images: {str(e)}")
            return []
    

    def fetch_chapters(self, manga_url):
        """Récupère la liste des chapitres pour un manga donné."""
        logger.debug(f"Récupération des chapitres depuis {manga_url}")
        
        # Récupérer la page du manga
        response = self.request_manager.get(manga_url)
        html = BeautifulSoup(response.text, 'html.parser')
        
        # Construire l'URL pour l'API des chapitres
        ajax_url = f"{manga_url}ajax/chapters/"
        logger.debug(f"URL des chapitres: {ajax_url}")
        
        # Faire la requête POST pour obtenir les chapitres
        headers = {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': self.BASE_URL,
            'Referer': manga_url,
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        response = self.request_manager.post(ajax_url, headers=headers)
        logger.debug(f"Status code de l'API: {response.status_code}")
        
        if response.status_code == 200:
            logger.debug("Réponse de l'API reçue")
            try:
                soup = BeautifulSoup(response.text, 'html.parser')
                chapters_list = soup.find_all('li', class_='wp-manga-chapter')
                logger.debug(f"Nombre de chapitres trouvés: {len(chapters_list)}")
                
                chapters = []
                for chapter in chapters_list:
                    link = chapter.find('a')
                    if link:
                        chapter_url = link['href']
                        chapter_title = link.text.strip()
                        chapters.append({
                            'url': chapter_url,
                            'title': chapter_title
                        })
                
                logger.info(f"{len(chapters)} chapitres valides trouvés pour {manga_url}")
                return chapters
                
            except Exception as e:
                logger.error(f"Erreur lors du parsing des chapitres: {str(e)}")
                return []
        else:
            logger.error(f"Erreur lors de la récupération des chapitres: {response.status_code}")
            return []
    

    def fetch_last_update(self, page=(1,)):
        response = self.request_manager.get(f'''{self.BASE_URL}/?s&post_type=wp-manga&m_orderby=latest''')
        html = BeautifulSoup(response.text, 'html.parser')
        all_manga = []
        for manga in html.find_all('div', {'class': 'c-tabs-item__content'}):
            title = manga.find('div', {'class': 'post-title'}).find('a').text
            url = manga.find('div', {'class': 'post-title'}).find('a')['href']
            all_manga.append({
                'title': title,
                'url': url })
        return all_manga

    
    def search_mangas(self, search_term):
        data = {
            'action': 'wp-manga-search-manga',
            'title': search_term }
        response = self.request_manager.post(f'{self.BASE_URL}/wp-admin/admin-ajax.php', data=data).json()
        all_manga = [{'title': manga['title'], 'url': manga['url']} for manga in response['data']]
        return all_manga

import os
import cv2
import easyocr
import torch

from tqdm import tqdm

class OCRController:
    __entity = None
    
    __reader = None
    __data = []
    __data_horizontal = [] # For storing results of horizontal processing

    def __new__(cls, detectLanguage):
        if cls.__entity is None:
            cls.__entity = super(OCRController, cls).__new__(cls)
            cls.__entity.__initialize(detectLanguage)
        return cls.__entity


    def __initialize(self, detectLanguage):
        # Verifica si CUDA (GPU) está disponible
        if torch.cuda.is_available():
            self.__device = 'cuda' # Corrected: original code had self.__device = 'cuda'
        else:
            self.__device = 'cpu'

        # Inicializa EasyOCR con el dispositivo apropiado
        if isinstance(detectLanguage, list):
            self.__reader = easyocr.Reader([L.lower() for L in detectLanguage], model_storage_directory="models", gpu=self.__device)
        else:
            self.__reader = easyocr.Reader([detectLanguage.lower()], model_storage_directory="models", gpu=self.__device)

    def __cleanData(self, data):
            """
            Recursively cleans the OCR data by removing empty sublists and ensuring that each sublist contains
            a list of coordinates and a string of text.

            Args:
                data (list): The OCR data to be cleaned.

            Returns:
                list: The cleaned OCR data.
            """
            
            if len(data) == 2 and type(list[-1]) is str: # 1 sublista con las coordenadas y 1 con el texto
                return [data]
            
            cleanList = []
            for d in data:
                if not d: # si la sublista está vacia
                    continue
                if len(d) == 2 and type(d[-1]) is str:
                    cleanList.append(d)
                    continue

                subLista = self.__cleanData(d)
                if subLista:
                    cleanList += subLista
            
            return cleanList

    def __detectCollisionY_aux(self, list1 , list2):
            dist = 10
            p0 = list1[0]
            p2 = list1[2]
            
            if p0[1] - dist < list2[0][1] and list2[0][1] < p2[1] + dist:
                return True
            if p0[1] - dist < list2[1][1] and list2[1][1] < p2[1] + dist:
                return True
            if p0[1] - dist < list2[2][1] and list2[2][1] < p2[1] + dist:
                return True
            if p0[1] - dist < list2[3][1] and list2[3][1] < p2[1] + dist:
                return True
            
            return False
    
    def __detectCollisionY(self, list1 , list2):
        """
            Determines if there is a collision between two lists of points in the Y-axis.

            Args:
            - list1: A list of 4 points representing a rectangle.
            - list2: A list of 4 points representing a rectangle.

            Returns:
            - True if there is a collision between the two lists in the Y-axis, False otherwise.
            """
        return (self.__detectCollisionY_aux(list1, list2) or self.__detectCollisionY_aux(list2, list1))

    def __mergeBoxes(self, boxList):
        mergedList = []
        merge = False

        while boxList: # mientras la lista no esté vacia
            subL1 = boxList.pop(0)
            mergedBox = []

            addBox = True # si se agrega la caja a la lista
            for i in range(len(boxList)):
                subL2 = boxList[i]
                if self.__detectCollisionY(subL1[0], subL2[0]): # pasa las coordenadas de la caja
                    mergedBox = [[[min(subL1[0][0][0], subL2[0][0][0]), min(subL1[0][0][1], subL2[0][0][1])], 
                                [max(subL1[0][1][0], subL2[0][1][0]), min(subL1[0][1][1], subL2[0][1][1])], 
                                [max(subL1[0][2][0], subL2[0][2][0]), max(subL1[0][2][1], subL2[0][2][1])], 
                                [min(subL1[0][3][0], subL2[0][3][0]), max(subL1[0][3][1], subL2[0][3][1])]], subL1[1] + ". " + subL2[1]]
                    mergedList.append(mergedBox)

                    boxList.pop(i)

                    merge = True
                    addBox = False
                    break

            if addBox:
                mergedList.append(subL1)
        
        if merge:
            return self.__mergeBoxes(mergedList)
        return mergedList

    def __createMissingBoxes(self, boxList, img):
            """
            Creates missing bounding boxes in a list of bounding boxes.

            Args:
                boxList (list): List of bounding boxes.
                img (numpy array): The image from which the bounding boxes were extracted.

            Returns:
                list: List of bounding boxes with missing boxes added.
            """
            height, width, _ = img.shape

            newList = []
            y = 0
            for b in boxList:
                if y < b[0][0][1]:
                    newList.append([[[0, y], [width, y], [width, b[0][0][1]], [0, b[0][0][1]]], ""])

                y = b[0][2][1]
                newList.append(b)

            if y < height:
                newList.append([[[0, y], [width, y], [width, height], [0, height]], ""])
            
            return newList

    def __verifyData(self, data, img):
        height, width, _ = img.shape

        newDataList = []
        for d in data:
            addData = True
            for i in range(len(d[0])):
                if d[0][i][1] > height or d[0][i][0] > width or d[0][i][1] < 0 or d[0][i][0] < 0:
                    addData = False
                    break
            
            if not addData or d[0][0][0] == d[0][1][0] or d[0][0][1] == d[0][3][1]:
                continue
            newDataList.append(d)
        
        return newDataList

    def __process(self, img):
            """
            Processes the given image using OCR to extract text data from it.
            This method handles the VERTICAL processing pass.

            Args:
                img: A numpy array representing the image to be processed.

            Returns:
                None
            """
            height, width, _ = img.shape
            maxHeight = 2560
            
            if height > maxHeight:
                for y1 in tqdm(range(0, height, int(maxHeight * 0.8)), desc = "Processing split image with OCR "):
                    y2 = y1 + maxHeight
                    if y2 > height:
                        y2 = height

                    splitImage = img[y1:y2, 0:width]
                    newData = self.__cleanData(self.__reader.readtext(splitImage, paragraph=True, batch_size=5))

                    for d in newData:
                        for i in range(len(d[0])):
                            d[0][i][1] += y1
                    self.__data += self.__mergeBoxes(newData)
            else:
                self.__data = self.__cleanData(self.__reader.readtext(img, paragraph=True, batch_size=5))
            self.__data = self.__verifyData(self.__mergeBoxes(self.__data), img)
            self.__data.sort(key=lambda y: y[0][0][1])


    def __detectCollisionX_aux(self, box1_coords, box2_coords):
        """
        Helper to check for horizontal merging suitability between two boxes' coordinates.
        Merges if they are horizontally adjacent (within dist_x_max_gap) 
        and have significant vertical overlap (min_vertical_overlap_ratio).
        """
        dist_x_max_gap = 20
        min_vertical_overlap_ratio = 0.6

        b1_x0, b1_x1 = box1_coords[0][0], box1_coords[1][0]
        b1_y0, b1_y1 = box1_coords[0][1], box1_coords[2][1]
        b1_height = b1_y1 - b1_y0

        b2_x0, b2_x1 = box2_coords[0][0], box2_coords[1][0]
        b2_y0, b2_y1 = box2_coords[0][1], box2_coords[2][1]
        b2_height = b2_y1 - b2_y0

        if b1_height <= 0 or b2_height <= 0: return False

        overlap_y_start = max(b1_y0, b2_y0)
        overlap_y_end = min(b1_y1, b2_y1)
        overlap_length = overlap_y_end - overlap_y_start

        if overlap_length <= 0: return False

        shorter_height = min(b1_height, b2_height)
        if shorter_height == 0: return False
        if (overlap_length / shorter_height) < min_vertical_overlap_ratio:
            return False

        if b1_x1 <= b2_x0:
            gap = b2_x0 - b1_x1
            if gap >= 0 and gap <= dist_x_max_gap:
                return True
        elif b2_x1 <= b1_x0:
            gap = b1_x0 - b2_x1
            if gap >= 0 and gap <= dist_x_max_gap:
                return True
        else:
            return True 
            
        return False

    def __detectCollisionX(self, list1, list2):
        """
        Determines if there is a collision between two lists of points (boxes) in the X-axis
        for horizontal merging. list1 and list2 are [coordinates, text] pairs.
        """
        return self.__detectCollisionX_aux(list1[0], list2[0])

    def __mergeBoxes_horizontal(self, boxList_input):
        """
        Merges bounding boxes horizontally. Adapted from __mergeBoxes.
        """
        current_pass_results = []
        did_merge_in_this_pass = False
        
        mutable_box_list = list(boxList_input) 

        while mutable_box_list:
            box1 = mutable_box_list.pop(0)

            i = 0
            while i < len(mutable_box_list):
                box2 = mutable_box_list[i]
                if self.__detectCollisionX(box1, box2):
                    coords1, text1 = box1
                    coords2, text2 = box2
                    all_x = [p[0] for p in coords1] + [p[0] for p in coords2]
                    all_y = [p[1] for p in coords1] + [p[1] for p in coords2]
                    min_x, max_x = min(all_x), max(all_x)
                    min_y, max_y = min(all_y), max(all_y)
                    merged_coords = [[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]]
                    
                    box1 = [merged_coords, text1 + ". " + text2]
                    
                    mutable_box_list.pop(i)
                    did_merge_in_this_pass = True
                else:
                    i += 1
            
            current_pass_results.append(box1)

        if did_merge_in_this_pass:
            return self.__mergeBoxes_horizontal(current_pass_results)
        else:
            return current_pass_results

    def __createMissingBoxes_horizontal(self, boxList, img_strip):
        """
        Creates missing bounding boxes horizontally within a vertical image strip.
        """
        strip_height, strip_width, _ = img_strip.shape
        if strip_width == 0: return []

        newList = []
        current_x = 0 
        
        boxList.sort(key=lambda b: b[0][0][0])

        for b in boxList:
            box_left_x = b[0][0][0]
            if current_x < box_left_x:

                if box_left_x > current_x :
                    newList.append([[[current_x, 0], [box_left_x, 0], [box_left_x, strip_height], [current_x, strip_height]], ""])
            
            newList.append(b)
            current_x = b[0][1][0]

        if current_x < strip_width:
            if strip_width > current_x:
                newList.append([[[current_x, 0], [strip_width, 0], [strip_width, strip_height], [current_x, strip_height]], ""])
        
        return newList

    def __process_horizontal(self, img_strip):
        """
        Processes a single vertical image strip horizontally using OCR.
        Populates self.__data_horizontal and returns it.
        """
        strip_height, strip_width, _ = img_strip.shape
        if strip_height == 0 or strip_width == 0:
            self.__data_horizontal = []
            return []

        raw_ocr_data = self.__reader.readtext(img_strip, paragraph=True, batch_size=5)
        
        self.__data_horizontal = self.__cleanData(raw_ocr_data)
        self.__data_horizontal = self.__verifyData(self.__data_horizontal, img_strip)
        self.__data_horizontal = self.__mergeBoxes_horizontal(self.__data_horizontal)
        self.__data_horizontal.sort(key=lambda y: y[0][0][0]) # Sort by x of top-left point
        
        return self.__data_horizontal
    
    # --- END NEW HORIZONTAL PROCESSING METHODS ---

    def __deduplicate_horizontal_segments(self, segment_list, tolerance=5):
        """
        Deduplicates a list of horizontal segments.
        Prioritizes segments with text, then wider segments, for the same starting x-coordinate.
        Removes segments that are effectively contained within an already chosen segment
        or do not offer significant new coverage.
        """
        if not segment_list:
            return []
        sorted_list = sorted(
            segment_list,
            key=lambda s: (s[0][0][0], not bool(s[1]), -(s[0][1][0] - s[0][0][0]))
        )

        if not sorted_list:
            return []

        deduplicated_list = [sorted_list[0]]

        for i in range(1, len(sorted_list)):
            current_segment = sorted_list[i]
            last_kept_segment = deduplicated_list[-1]

            curr_x0 = current_segment[0][0][0]
            curr_x1 = current_segment[0][1][0]

            last_x0 = last_kept_segment[0][0][0]
            last_x1 = last_kept_segment[0][1][0]

            if curr_x0 >= last_x1 - tolerance:
                deduplicated_list.append(current_segment)
                continue

            if curr_x0 >= last_x0 - tolerance and curr_x1 <= last_x1 + tolerance:
                pass
            else:
                deduplicated_list.append(current_segment)
        
        return deduplicated_list

    def splitImage(self, imagesInfo, output_dir, output_dir2 = None):
            """
            Splits input images first vertically, then each vertical strip horizontally, based on OCR.
            Saves final segmented sub-images.
            """
            numImagen = 1
            
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            if output_dir2 and not os.path.exists(output_dir2):
                os.makedirs(output_dir2)

            for imgInf_idx, imgInf in enumerate(tqdm(imagesInfo, desc="Processing images")):
                original_img, original_imageName = imgInf
                if original_img is None or original_img.size == 0:
                    print(f"Warning: Skipping empty or invalid image {original_imageName}")
                    continue
                
                self.__data = [] 
                self.__process(original_img)
                
                vertical_boxList = self.__createMissingBoxes(self.__data, original_img)

                for v_strip_idx, v_box_info in enumerate(tqdm(vertical_boxList, 
                                                              desc=f"Vert. strips from {original_imageName}", 
                                                              leave=False)):
                    v_coords = v_box_info[0]
                    
                    y_start, y_end = v_coords[0][1], v_coords[2][1]
                    x_start, x_end = 0, original_img.shape[1] 

                    if not (0 <= y_start < y_end <= original_img.shape[0] and \
                            0 <= x_start < x_end <= original_img.shape[1]):
                        continue
                        
                    vertical_strip_img = original_img[y_start:y_end, x_start:x_end]

                    if vertical_strip_img.size == 0 or vertical_strip_img.shape[0] == 0 or vertical_strip_img.shape[1] == 0:
                        continue

                    self.__data_horizontal = [] 
                    horizontal_ocr_boxes_in_strip = self.__process_horizontal(vertical_strip_img)
                    
                    raw_horizontal_segment_list = self.__createMissingBoxes_horizontal(horizontal_ocr_boxes_in_strip, vertical_strip_img)
                    
                    horizontal_segment_list = self.__deduplicate_horizontal_segments(raw_horizontal_segment_list)

                    for h_seg_idx, h_segment_info in enumerate(tqdm(horizontal_segment_list, 
                                                                      desc=f"Horiz. segments in strip {v_strip_idx}", 
                                                                      leave=False)):
                        h_coords_in_strip = h_segment_info[0]
                        h_text_in_segment = bool(h_segment_info[1]) 

                        seg_y_start_in_strip, seg_y_end_in_strip = 0, vertical_strip_img.shape[0]
                        seg_x_start_in_strip, seg_x_end_in_strip = h_coords_in_strip[0][0], h_coords_in_strip[1][0]
                        
                        if not (0 <= seg_y_start_in_strip < seg_y_end_in_strip <= vertical_strip_img.shape[0] and \
                                0 <= seg_x_start_in_strip < seg_x_end_in_strip <= vertical_strip_img.shape[1]):
                            continue

                        final_segment_img = vertical_strip_img[
                            seg_y_start_in_strip : seg_y_end_in_strip,
                            seg_x_start_in_strip : seg_x_end_in_strip
                        ]

                        if final_segment_img.size == 0 or final_segment_img.shape[0] == 0 or final_segment_img.shape[1] == 0:
                            continue
                        
                        current_dir = output_dir
                        if output_dir2 and not h_text_in_segment: 
                            current_dir = output_dir2

                        final_imageName_output = f"{original_imageName}_{numImagen}.jpg"
                        try:
                            cv2.imwrite(os.path.join(current_dir, final_imageName_output), final_segment_img)
                            numImagen += 1
                        except Exception as e:
                            print(f"Error writing image {final_imageName_output} to {current_dir}: {e}")